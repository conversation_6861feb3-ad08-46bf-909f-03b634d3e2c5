"use client";

import React, { useState, useEffect, useMemo } from "react";
import { useRouter } from "next/navigation";
import { useAdminHeader } from "@/hooks/use-admin-header";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Progress } from "@/components/ui/progress";
import { 
  ArrowLeft,
  ArrowRight,
  Save,
  Settings,
  FileText,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>ert<PERSON><PERSON><PERSON>,
  Info,
  Plus,
  X,
  Copy
} from "lucide-react";
import { AssetCategory } from "@/lib/modules/asset-types/types";
import { toast } from "@/components/ui/use-toast";

interface CreateAssetTypeFormData {
  // Basic Information
  name: string;
  code: string;
  description: string;
  categoryId: string;
  subcategory: string;
  icon: string;
  color: string;
  isActive: boolean;
  tags: string[];
  
  // Configuration Options
  includeCustomFields: boolean;
  includeLifecycleStages: boolean;
  includeMaintenanceSchedules: boolean;
  includeDepreciationSettings: boolean;
  includeWorkflows: boolean;
}

const CREATION_STEPS = [
  { id: "basic", title: "Basic Information", description: "Name, code, and category" },
  { id: "appearance", title: "Appearance", description: "Icon, color, and visual settings" },
  { id: "configuration", title: "Configuration", description: "Choose what to configure" },
  { id: "review", title: "Review", description: "Review and create" },
];

const PREDEFINED_COLORS = [
  "#3B82F6", "#EF4444", "#10B981", "#F59E0B", "#8B5CF6",
  "#EC4899", "#06B6D4", "#84CC16", "#F97316", "#6366F1"
];

const PREDEFINED_ICONS = [
  "Settings", "Laptop", "Car", "Building", "Wrench", "Monitor",
  "Truck", "Package", "HardDrive", "Printer", "Phone", "Camera"
];

export default function CreateAssetTypePage() {
  const router = useRouter();
  const [currentStep, setCurrentStep] = useState(0);
  const [categories, setCategories] = useState<AssetCategory[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});
  
  const [formData, setFormData] = useState<CreateAssetTypeFormData>({
    name: "",
    code: "",
    description: "",
    categoryId: "",
    subcategory: "",
    icon: "Settings",
    color: "#3B82F6",
    isActive: true,
    tags: [],
    includeCustomFields: false,
    includeLifecycleStages: false,
    includeMaintenanceSchedules: false,
    includeDepreciationSettings: false,
    includeWorkflows: false,
  });

  // Load categories on mount
  useEffect(() => {
    loadCategories();
  }, []);

  const loadCategories = async () => {
    try {
      const response = await fetch("/api/asset-categories");
      if (response.ok) {
        const data = await response.json();
        setCategories(data);
      }
    } catch (error) {
      console.error("Error loading categories:", error);
    }
  };

  // Auto-generate code from name
  useEffect(() => {
    if (formData.name && !formData.code) {
      const generatedCode = formData.name
        .toLowerCase()
        .replace(/[^a-z0-9\s]/g, "")
        .replace(/\s+/g, "_")
        .substring(0, 20);
      setFormData(prev => ({ ...prev, code: generatedCode }));
    }
  }, [formData.name]);

  // Header configuration
  const headerConfig = useMemo(() => ({
    title: "Create Asset Type",
    description: "Set up a new asset type with configurations",
    breadcrumbs: [
      { label: "Admin", url: "/admin" },
      { label: "Asset Types", url: "/admin/asset-types" },
      { label: "Create", url: "/admin/asset-types/create" },
    ],
    variant: 'create' as const,
    actions: [
      <Button key="back" variant="outline" onClick={() => router.back()}>
        <ArrowLeft className="mr-2 h-4 w-4" />
        Back
      </Button>,
    ],
  }), [router]);

  useAdminHeader(headerConfig);

  const validateStep = (step: number): boolean => {
    const newErrors: Record<string, string> = {};

    switch (step) {
      case 0: // Basic Information
        if (!formData.name.trim()) {
          newErrors.name = "Asset type name is required";
        }
        if (!formData.code.trim()) {
          newErrors.code = "Asset type code is required";
        }
        if (!formData.categoryId) {
          newErrors.categoryId = "Category is required";
        }
        break;
      case 1: // Appearance
        if (!formData.icon) {
          newErrors.icon = "Icon is required";
        }
        if (!formData.color) {
          newErrors.color = "Color is required";
        }
        break;
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleNext = () => {
    if (validateStep(currentStep)) {
      setCurrentStep(prev => Math.min(prev + 1, CREATION_STEPS.length - 1));
    }
  };

  const handlePrevious = () => {
    setCurrentStep(prev => Math.max(prev - 1, 0));
  };

  const handleSubmit = async () => {
    if (!validateStep(currentStep)) return;

    setIsLoading(true);
    try {
      const selectedCategory = categories.find(cat => cat.id === formData.categoryId);
      
      const assetTypeData = {
        name: formData.name,
        code: formData.code,
        description: formData.description,
        category: selectedCategory!,
        subcategory: formData.subcategory || undefined,
        icon: formData.icon,
        color: formData.color,
        isActive: formData.isActive,
        tags: formData.tags,
        customFields: [],
        lifecycleStages: [],
        maintenanceSchedules: [],
        depreciationSettings: {
          method: "straight_line" as const,
          usefulLife: 5,
          usefulLifeUnit: "years" as const,
          salvageValue: 0,
          salvageValueType: "percentage" as const,
          startDate: new Date().toISOString(),
          isActive: true,
        },
        createdBy: "current-user", // TODO: Get from auth context
      };

      const response = await fetch("/api/asset-types", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(assetTypeData),
      });

      if (!response.ok) {
        throw new Error("Failed to create asset type");
      }

      const result = await response.json();
      
      toast({
        title: "Success",
        description: "Asset type created successfully.",
      });

      // Navigate to configuration if any options were selected
      const hasConfigurations = [
        formData.includeCustomFields,
        formData.includeLifecycleStages,
        formData.includeMaintenanceSchedules,
        formData.includeDepreciationSettings,
        formData.includeWorkflows,
      ].some(Boolean);

      if (hasConfigurations) {
        router.push(`/admin/asset-types/${result.id}`);
      } else {
        router.push("/admin/asset-types");
      }

    } catch (error) {
      console.error("Error creating asset type:", error);
      toast({
        title: "Error",
        description: "Failed to create asset type. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const addTag = (tag: string) => {
    if (tag && !formData.tags.includes(tag)) {
      setFormData(prev => ({
        ...prev,
        tags: [...prev.tags, tag]
      }));
    }
  };

  const removeTag = (tagToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove)
    }));
  };

  const progress = ((currentStep + 1) / CREATION_STEPS.length) * 100;

  return (
    <div className="flex-1 space-y-6 p-4 md:p-8 pt-6">
      {/* Progress Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>{CREATION_STEPS[currentStep].title}</CardTitle>
              <p className="text-sm text-muted-foreground">
                {CREATION_STEPS[currentStep].description}
              </p>
            </div>
            <div className="text-sm text-muted-foreground">
              Step {currentStep + 1} of {CREATION_STEPS.length}
            </div>
          </div>
          <div className="space-y-2">
            <Progress value={progress} className="w-full" />
            <div className="flex justify-between text-xs text-muted-foreground">
              {CREATION_STEPS.map((step, index) => (
                <span 
                  key={step.id}
                  className={index <= currentStep ? "text-primary font-medium" : ""}
                >
                  {step.title}
                </span>
              ))}
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Step Content */}
      <Card>
        <CardContent className="p-6">
          {currentStep === 0 && (
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="name">Asset Type Name *</Label>
                  <Input
                    id="name"
                    value={formData.name}
                    onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                    placeholder="e.g., Laptop Computer"
                    className={errors.name ? "border-red-500" : ""}
                  />
                  {errors.name && (
                    <p className="text-sm text-red-500">{errors.name}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="code">Asset Type Code *</Label>
                  <Input
                    id="code"
                    value={formData.code}
                    onChange={(e) => setFormData(prev => ({ ...prev, code: e.target.value }))}
                    placeholder="e.g., laptop_computer"
                    className={errors.code ? "border-red-500" : ""}
                  />
                  {errors.code && (
                    <p className="text-sm text-red-500">{errors.code}</p>
                  )}
                  <p className="text-xs text-muted-foreground">
                    Auto-generated from name, but you can customize it
                  </p>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                  placeholder="Describe this asset type..."
                  rows={3}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="category">Category *</Label>
                  <Select 
                    value={formData.categoryId} 
                    onValueChange={(value) => setFormData(prev => ({ ...prev, categoryId: value }))}
                  >
                    <SelectTrigger className={errors.categoryId ? "border-red-500" : ""}>
                      <SelectValue placeholder="Select a category" />
                    </SelectTrigger>
                    <SelectContent>
                      {categories.map((category) => (
                        <SelectItem key={category.id} value={category.id}>
                          {category.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {errors.categoryId && (
                    <p className="text-sm text-red-500">{errors.categoryId}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="subcategory">Subcategory</Label>
                  <Input
                    id="subcategory"
                    value={formData.subcategory}
                    onChange={(e) => setFormData(prev => ({ ...prev, subcategory: e.target.value }))}
                    placeholder="e.g., Desktop, Mobile"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label>Tags</Label>
                <div className="flex flex-wrap gap-2 mb-2">
                  {formData.tags.map((tag) => (
                    <Badge key={tag} variant="secondary" className="flex items-center gap-1">
                      {tag}
                      <X 
                        className="h-3 w-3 cursor-pointer" 
                        onClick={() => removeTag(tag)}
                      />
                    </Badge>
                  ))}
                </div>
                <Input
                  placeholder="Add tags (press Enter)"
                  onKeyDown={(e) => {
                    if (e.key === "Enter") {
                      e.preventDefault();
                      const target = e.target as HTMLInputElement;
                      addTag(target.value.trim());
                      target.value = "";
                    }
                  }}
                />
                <p className="text-xs text-muted-foreground">
                  Press Enter to add tags. Tags help with organization and searching.
                </p>
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  id="isActive"
                  checked={formData.isActive}
                  onCheckedChange={(checked) => setFormData(prev => ({ ...prev, isActive: checked }))}
                />
                <Label htmlFor="isActive">Active</Label>
                <p className="text-sm text-muted-foreground">
                  Active asset types can be used for creating new assets
                </p>
              </div>
            </div>
          )}

          {currentStep === 1 && (
            <div className="space-y-6">
              <div className="space-y-4">
                <div>
                  <Label>Icon *</Label>
                  <div className="grid grid-cols-6 gap-3 mt-2">
                    {PREDEFINED_ICONS.map((iconName) => (
                      <button
                        key={iconName}
                        type="button"
                        onClick={() => setFormData(prev => ({ ...prev, icon: iconName }))}
                        className={`p-3 border rounded-lg flex items-center justify-center hover:bg-muted transition-colors ${
                          formData.icon === iconName ? "border-primary bg-primary/10" : ""
                        }`}
                      >
                        <Settings className="h-5 w-5" />
                      </button>
                    ))}
                  </div>
                  {errors.icon && (
                    <p className="text-sm text-red-500 mt-1">{errors.icon}</p>
                  )}
                </div>

                <div>
                  <Label>Color *</Label>
                  <div className="grid grid-cols-10 gap-2 mt-2">
                    {PREDEFINED_COLORS.map((color) => (
                      <button
                        key={color}
                        type="button"
                        onClick={() => setFormData(prev => ({ ...prev, color }))}
                        className={`w-8 h-8 rounded-full border-2 ${
                          formData.color === color ? "border-gray-900 scale-110" : "border-gray-300"
                        }`}
                        style={{ backgroundColor: color }}
                      />
                    ))}
                  </div>
                  <div className="mt-3">
                    <Input
                      type="color"
                      value={formData.color}
                      onChange={(e) => setFormData(prev => ({ ...prev, color: e.target.value }))}
                      className="w-20 h-10"
                    />
                  </div>
                  {errors.color && (
                    <p className="text-sm text-red-500 mt-1">{errors.color}</p>
                  )}
                </div>

                <div className="p-4 border rounded-lg bg-muted">
                  <h4 className="font-medium mb-2">Preview</h4>
                  <div className="flex items-center gap-3">
                    <div
                      className="w-10 h-10 rounded-lg flex items-center justify-center"
                      style={{ backgroundColor: formData.color + "20", color: formData.color }}
                    >
                      <Settings className="h-5 w-5" />
                    </div>
                    <div>
                      <p className="font-medium">{formData.name || "Asset Type Name"}</p>
                      <p className="text-sm text-muted-foreground">{formData.code || "asset_type_code"}</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {currentStep === 2 && (
            <div className="space-y-6">
              <Alert>
                <Info className="h-4 w-4" />
                <AlertDescription>
                  Choose which configurations you want to set up for this asset type.
                  You can always configure these later from the asset type details page.
                </AlertDescription>
              </Alert>

              <div className="grid gap-4">
                <div className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex items-center gap-3">
                    <FileText className="h-5 w-5 text-blue-500" />
                    <div>
                      <h4 className="font-medium">Custom Fields</h4>
                      <p className="text-sm text-muted-foreground">
                        Add custom fields to capture additional asset information
                      </p>
                    </div>
                  </div>
                  <Switch
                    checked={formData.includeCustomFields}
                    onCheckedChange={(checked) =>
                      setFormData(prev => ({ ...prev, includeCustomFields: checked }))
                    }
                  />
                </div>

                <div className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex items-center gap-3">
                    <Settings className="h-5 w-5 text-purple-500" />
                    <div>
                      <h4 className="font-medium">Lifecycle Stages</h4>
                      <p className="text-sm text-muted-foreground">
                        Define stages for asset lifecycle management
                      </p>
                    </div>
                  </div>
                  <Switch
                    checked={formData.includeLifecycleStages}
                    onCheckedChange={(checked) =>
                      setFormData(prev => ({ ...prev, includeLifecycleStages: checked }))
                    }
                  />
                </div>

                <div className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex items-center gap-3">
                    <Settings className="h-5 w-5 text-orange-500" />
                    <div>
                      <h4 className="font-medium">Maintenance Schedules</h4>
                      <p className="text-sm text-muted-foreground">
                        Set up automated maintenance scheduling
                      </p>
                    </div>
                  </div>
                  <Switch
                    checked={formData.includeMaintenanceSchedules}
                    onCheckedChange={(checked) =>
                      setFormData(prev => ({ ...prev, includeMaintenanceSchedules: checked }))
                    }
                  />
                </div>

                <div className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex items-center gap-3">
                    <Settings className="h-5 w-5 text-red-500" />
                    <div>
                      <h4 className="font-medium">Depreciation Settings</h4>
                      <p className="text-sm text-muted-foreground">
                        Configure depreciation calculation methods
                      </p>
                    </div>
                  </div>
                  <Switch
                    checked={formData.includeDepreciationSettings}
                    onCheckedChange={(checked) =>
                      setFormData(prev => ({ ...prev, includeDepreciationSettings: checked }))
                    }
                  />
                </div>

                <div className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex items-center gap-3">
                    <Settings className="h-5 w-5 text-indigo-500" />
                    <div>
                      <h4 className="font-medium">Workflows</h4>
                      <p className="text-sm text-muted-foreground">
                        Set up automated workflows and triggers
                      </p>
                    </div>
                  </div>
                  <Switch
                    checked={formData.includeWorkflows}
                    onCheckedChange={(checked) =>
                      setFormData(prev => ({ ...prev, includeWorkflows: checked }))
                    }
                  />
                </div>
              </div>
            </div>
          )}

          {currentStep === 3 && (
            <div className="space-y-6">
              <Alert>
                <CheckCircle className="h-4 w-4" />
                <AlertDescription>
                  Review your asset type configuration before creating it.
                </AlertDescription>
              </Alert>

              <div className="grid gap-6">
                <div>
                  <h3 className="font-medium mb-3">Basic Information</h3>
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="text-muted-foreground">Name:</span>
                      <p className="font-medium">{formData.name}</p>
                    </div>
                    <div>
                      <span className="text-muted-foreground">Code:</span>
                      <p className="font-medium">{formData.code}</p>
                    </div>
                    <div>
                      <span className="text-muted-foreground">Category:</span>
                      <p className="font-medium">
                        {categories.find(cat => cat.id === formData.categoryId)?.name}
                      </p>
                    </div>
                    <div>
                      <span className="text-muted-foreground">Status:</span>
                      <Badge variant={formData.isActive ? "default" : "secondary"}>
                        {formData.isActive ? "Active" : "Inactive"}
                      </Badge>
                    </div>
                  </div>
                  {formData.description && (
                    <div className="mt-3">
                      <span className="text-muted-foreground text-sm">Description:</span>
                      <p className="text-sm">{formData.description}</p>
                    </div>
                  )}
                  {formData.tags.length > 0 && (
                    <div className="mt-3">
                      <span className="text-muted-foreground text-sm">Tags:</span>
                      <div className="flex flex-wrap gap-1 mt-1">
                        {formData.tags.map((tag) => (
                          <Badge key={tag} variant="outline" className="text-xs">
                            {tag}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}
                </div>

                <Separator />

                <div>
                  <h3 className="font-medium mb-3">Appearance</h3>
                  <div className="flex items-center gap-3">
                    <div
                      className="w-12 h-12 rounded-lg flex items-center justify-center"
                      style={{ backgroundColor: formData.color + "20", color: formData.color }}
                    >
                      <Settings className="h-6 w-6" />
                    </div>
                    <div>
                      <p className="font-medium">{formData.name}</p>
                      <p className="text-sm text-muted-foreground">
                        Icon: {formData.icon} • Color: {formData.color}
                      </p>
                    </div>
                  </div>
                </div>

                <Separator />

                <div>
                  <h3 className="font-medium mb-3">Configuration Options</h3>
                  <div className="grid gap-2">
                    {[
                      { key: "includeCustomFields", label: "Custom Fields", icon: FileText },
                      { key: "includeLifecycleStages", label: "Lifecycle Stages", icon: Settings },
                      { key: "includeMaintenanceSchedules", label: "Maintenance Schedules", icon: Settings },
                      { key: "includeDepreciationSettings", label: "Depreciation Settings", icon: Settings },
                      { key: "includeWorkflows", label: "Workflows", icon: Settings },
                    ].map((option) => {
                      const IconComponent = option.icon;
                      const isIncluded = formData[option.key as keyof CreateAssetTypeFormData] as boolean;
                      return (
                        <div key={option.key} className="flex items-center gap-2 text-sm">
                          <IconComponent className="h-4 w-4" />
                          <span className={isIncluded ? "text-green-600" : "text-muted-foreground"}>
                            {option.label}
                          </span>
                          {isIncluded ? (
                            <CheckCircle className="h-4 w-4 text-green-600" />
                          ) : (
                            <span className="text-muted-foreground">(Skip for now)</span>
                          )}
                        </div>
                      );
                    })}
                  </div>
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Navigation */}
      <div className="flex justify-between">
        <Button
          variant="outline"
          onClick={handlePrevious}
          disabled={currentStep === 0}
        >
          <ArrowLeft className="mr-2 h-4 w-4" />
          Previous
        </Button>

        {currentStep < CREATION_STEPS.length - 1 ? (
          <Button onClick={handleNext}>
            Next
            <ArrowRight className="ml-2 h-4 w-4" />
          </Button>
        ) : (
          <Button onClick={handleSubmit} disabled={isLoading}>
            {isLoading ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Creating...
              </>
            ) : (
              <>
                <Save className="mr-2 h-4 w-4" />
                Create Asset Type
              </>
            )}
          </Button>
        )}
      </div>
    </div>
  );
}
