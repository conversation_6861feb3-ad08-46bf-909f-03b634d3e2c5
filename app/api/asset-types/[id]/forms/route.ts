import { NextRequest, NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import { z } from "zod";

// Validation schemas
const AssetTypeFormCreateSchema = z.object({
  formId: z.string(),
  operationType: z.string(),
  version: z.number().optional().default(1),
  isDefault: z.boolean().optional().default(false),
  isActive: z.boolean().optional().default(true),
  createdBy: z.string(),
});

const AssetTypeFormUpdateSchema = z.object({
  formId: z.string().optional(),
  operationType: z.string().optional(),
  version: z.number().optional(),
  isDefault: z.boolean().optional(),
  isActive: z.boolean().optional(),
});

// GET /api/asset-types/[id]/forms - Get forms for asset type
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const assetTypeId = params.id;

    // Validate asset type exists
    const assetType = await prisma.assetType.findUnique({
      where: { id: assetTypeId },
      select: { id: true, name: true },
    });

    if (!assetType) {
      return NextResponse.json(
        { error: "Asset type not found" },
        { status: 404 }
      );
    }

    // Get forms for this asset type
    const forms = await prisma.assetTypeForm.findMany({
      where: { assetTypeId },
      include: {
        form: {
          select: {
            id: true,
            name: true,
            description: true,
            sections: true,
            settings: true,
            version: true,
            isActive: true,
            createdAt: true,
            updatedAt: true,
          },
        },
      },
      orderBy: [
        { operationType: "asc" },
        { isDefault: "desc" },
        { createdAt: "desc" },
      ],
    });

    // Parse JSON fields and format response
    const formattedForms = forms.map((form) => ({
      ...form,
      form: {
        ...form.form,
        sections: JSON.parse(form.form.sections as string),
        settings: JSON.parse(form.form.settings as string),
      },
    }));

    return NextResponse.json(formattedForms);

  } catch (error) {
    console.error("Error fetching asset type forms:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

// POST /api/asset-types/[id]/forms - Create form association
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const assetTypeId = params.id;
    const body = await request.json();

    // Validate request body
    const validatedData = AssetTypeFormCreateSchema.parse(body);

    // Validate asset type exists
    const assetType = await prisma.assetType.findUnique({
      where: { id: assetTypeId },
      select: { id: true },
    });

    if (!assetType) {
      return NextResponse.json(
        { error: "Asset type not found" },
        { status: 404 }
      );
    }

    // Validate form exists
    const form = await prisma.formDefinition.findUnique({
      where: { id: validatedData.formId },
      select: { id: true },
    });

    if (!form) {
      return NextResponse.json(
        { error: "Form definition not found" },
        { status: 404 }
      );
    }

    // Check if association already exists
    const existingAssociation = await prisma.assetTypeForm.findFirst({
      where: {
        assetTypeId,
        operationType: validatedData.operationType,
      },
    });

    if (existingAssociation) {
      return NextResponse.json(
        { error: "Form association already exists for this operation type" },
        { status: 400 }
      );
    }

    // If this is set as default, unset other defaults for the same operation type
    if (validatedData.isDefault) {
      await prisma.assetTypeForm.updateMany({
        where: {
          assetTypeId,
          operationType: validatedData.operationType,
          isDefault: true,
        },
        data: {
          isDefault: false,
        },
      });
    }

    // Create the association
    const assetTypeForm = await prisma.assetTypeForm.create({
      data: {
        assetTypeId,
        ...validatedData,
      },
      include: {
        form: {
          select: {
            id: true,
            name: true,
            description: true,
            sections: true,
            settings: true,
            version: true,
            isActive: true,
          },
        },
      },
    });

    // Parse JSON fields
    const formattedForm = {
      ...assetTypeForm,
      form: {
        ...assetTypeForm.form,
        sections: JSON.parse(assetTypeForm.form.sections as string),
        settings: JSON.parse(assetTypeForm.form.settings as string),
      },
    };

    return NextResponse.json(formattedForm, { status: 201 });

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Validation error", details: error.errors },
        { status: 400 }
      );
    }

    console.error("Error creating asset type form:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

// PUT /api/asset-types/[id]/forms - Update form association
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const assetTypeId = params.id;
    const body = await request.json();
    const { formAssociationId, ...updateData } = body;

    if (!formAssociationId) {
      return NextResponse.json(
        { error: "Form association ID is required" },
        { status: 400 }
      );
    }

    // Validate update data
    const validatedData = AssetTypeFormUpdateSchema.parse(updateData);

    // Validate association exists and belongs to this asset type
    const existingAssociation = await prisma.assetTypeForm.findFirst({
      where: {
        id: formAssociationId,
        assetTypeId,
      },
    });

    if (!existingAssociation) {
      return NextResponse.json(
        { error: "Form association not found" },
        { status: 404 }
      );
    }

    // If setting as default, unset other defaults for the same operation type
    if (validatedData.isDefault) {
      const operationType = validatedData.operationType || existingAssociation.operationType;
      await prisma.assetTypeForm.updateMany({
        where: {
          assetTypeId,
          operationType,
          isDefault: true,
          id: { not: formAssociationId },
        },
        data: {
          isDefault: false,
        },
      });
    }

    // Update the association
    const updatedAssociation = await prisma.assetTypeForm.update({
      where: { id: formAssociationId },
      data: validatedData,
      include: {
        form: {
          select: {
            id: true,
            name: true,
            description: true,
            sections: true,
            settings: true,
            version: true,
            isActive: true,
          },
        },
      },
    });

    // Parse JSON fields
    const formattedForm = {
      ...updatedAssociation,
      form: {
        ...updatedAssociation.form,
        sections: JSON.parse(updatedAssociation.form.sections as string),
        settings: JSON.parse(updatedAssociation.form.settings as string),
      },
    };

    return NextResponse.json(formattedForm);

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Validation error", details: error.errors },
        { status: 400 }
      );
    }

    console.error("Error updating asset type form:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

// DELETE /api/asset-types/[id]/forms - Delete form association
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const assetTypeId = params.id;
    const { searchParams } = new URL(request.url);
    const formAssociationId = searchParams.get("formAssociationId");

    if (!formAssociationId) {
      return NextResponse.json(
        { error: "Form association ID is required" },
        { status: 400 }
      );
    }

    // Validate association exists and belongs to this asset type
    const existingAssociation = await prisma.assetTypeForm.findFirst({
      where: {
        id: formAssociationId,
        assetTypeId,
      },
    });

    if (!existingAssociation) {
      return NextResponse.json(
        { error: "Form association not found" },
        { status: 404 }
      );
    }

    // Delete the association
    await prisma.assetTypeForm.delete({
      where: { id: formAssociationId },
    });

    return NextResponse.json({ success: true });

  } catch (error) {
    console.error("Error deleting asset type form:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
