import type {
  AssetType,
  AssetCategory,
  CustomField,
  LifecycleStage,
  MaintenanceSchedule,
  DepreciationSettings,
  AssetTypeTemplate,
  AssetTypeMetrics,
  AssetTypeValidationResult,
} from "./types"
import { AssetTypeDbService } from "./db-service"
import { useAssetTypeStore } from "@/store/asset-type-store"

export class AssetTypeService {
  private static instance: AssetTypeService
  private dbService: AssetTypeDbService

  static getInstance(): AssetTypeService {
    if (!AssetTypeService.instance) {
      AssetTypeService.instance = new AssetTypeService()
    }
    return AssetTypeService.instance
  }

  constructor() {
    this.dbService = AssetTypeDbService.getInstance()
  }

  // Initialize service with data from database
  async initialize(): Promise<void> {
    try {
      const store = useAssetTypeStore.getState()
      store.setLoading(true)
      store.clearError()

      // Load categories first
      const categories = await this.dbService.getAllCategories()
      store.setCategories(categories)

      // Load asset types
      const assetTypes = await this.dbService.getAssetTypes()
      store.setAssetTypes(assetTypes)

      // Load templates
      const templates = await this.dbService.getTemplates()
      store.setTemplates(templates)

      // Load metrics
      const metrics = await this.dbService.getMetrics()
      store.setMetrics(metrics)

    } catch (error) {
      const store = useAssetTypeStore.getState()
      store.setError(error instanceof Error ? error.message : "Failed to initialize asset type service")
      throw error
    } finally {
      const store = useAssetTypeStore.getState()
      store.setLoading(false)
    }
  }

  // Asset Type CRUD Operations
  async createAssetType(assetType: Omit<AssetType, "id" | "createdAt" | "updatedAt" | "version">): Promise<AssetType> {
    try {
      const store = useAssetTypeStore.getState()
      store.setLoading(true)
      store.clearError()

      const newAssetType = await this.dbService.createAssetType(assetType)
      
      // Update store
      store.addAssetType(newAssetType)
      
      // Refresh metrics
      const metrics = await this.dbService.getMetrics()
      store.setMetrics(metrics)

      return newAssetType
    } catch (error) {
      const store = useAssetTypeStore.getState()
      const errorMessage = error instanceof Error ? error.message : "Failed to create asset type"
      store.setError(errorMessage)
      throw error
    } finally {
      const store = useAssetTypeStore.getState()
      store.setLoading(false)
    }
  }

  async updateAssetType(id: string, updates: Partial<AssetType>): Promise<AssetType | null> {
    try {
      const store = useAssetTypeStore.getState()
      store.setLoading(true)
      store.clearError()

      const updatedAssetType = await this.dbService.updateAssetType(id, updates)
      
      if (updatedAssetType) {
        // Update store
        store.updateAssetType(id, updatedAssetType)
        
        // Refresh metrics
        const metrics = await this.dbService.getMetrics()
        store.setMetrics(metrics)
      }

      return updatedAssetType
    } catch (error) {
      const store = useAssetTypeStore.getState()
      const errorMessage = error instanceof Error ? error.message : "Failed to update asset type"
      store.setError(errorMessage)
      throw error
    } finally {
      const store = useAssetTypeStore.getState()
      store.setLoading(false)
    }
  }

  async deleteAssetType(id: string): Promise<boolean> {
    try {
      const store = useAssetTypeStore.getState()
      store.setLoading(true)
      store.clearError()

      const success = await this.dbService.deleteAssetType(id)
      
      if (success) {
        // Update store
        store.removeAssetType(id)
        
        // Refresh metrics
        const metrics = await this.dbService.getMetrics()
        store.setMetrics(metrics)
      }

      return success
    } catch (error) {
      const store = useAssetTypeStore.getState()
      const errorMessage = error instanceof Error ? error.message : "Failed to delete asset type"
      store.setError(errorMessage)
      throw error
    } finally {
      const store = useAssetTypeStore.getState()
      store.setLoading(false)
    }
  }

  async getAssetType(id: string): Promise<AssetType | null> {
    try {
      const store = useAssetTypeStore.getState()
      
      // First check store
      const cachedAssetType = store.assetTypes.find((at) => at.id === id)
      if (cachedAssetType) {
        return cachedAssetType
      }

      // If not in store, fetch from database
      store.setLoading(true)
      const assetType = await this.dbService.getAssetType(id)
      
      if (assetType) {
        // Update store with fresh data
        store.addAssetType(assetType)
      }

      return assetType
    } catch (error) {
      const store = useAssetTypeStore.getState()
      const errorMessage = error instanceof Error ? error.message : "Failed to get asset type"
      store.setError(errorMessage)
      throw error
    } finally {
      const store = useAssetTypeStore.getState()
      store.setLoading(false)
    }
  }

  async getAssetTypes(filters?: {
    category?: string
    isActive?: boolean
    search?: string
  }): Promise<AssetType[]> {
    try {
      const store = useAssetTypeStore.getState()
      store.setLoading(true)
      store.clearError()

      // Update filters in store
      if (filters) {
        store.setFilters(filters)
      }

      const assetTypes = await this.dbService.getAssetTypes(filters)
      
      // Update store
      store.setAssetTypes(assetTypes)

      return assetTypes
    } catch (error) {
      const store = useAssetTypeStore.getState()
      const errorMessage = error instanceof Error ? error.message : "Failed to get asset types"
      store.setError(errorMessage)
      throw error
    } finally {
      const store = useAssetTypeStore.getState()
      store.setLoading(false)
    }
  }

  // Custom Fields Management
  async addCustomField(assetTypeId: string, field: Omit<CustomField, "id">): Promise<CustomField | null> {
    try {
      const store = useAssetTypeStore.getState()
      store.setLoading(true)
      store.clearError()

      const newField = await this.dbService.addCustomField(assetTypeId, field)
      
      if (newField) {
        // Update the asset type in store
        const updatedAssetType = await this.dbService.getAssetType(assetTypeId)
        if (updatedAssetType) {
          store.updateAssetType(assetTypeId, updatedAssetType)
        }
      }

      return newField
    } catch (error) {
      const store = useAssetTypeStore.getState()
      const errorMessage = error instanceof Error ? error.message : "Failed to add custom field"
      store.setError(errorMessage)
      throw error
    } finally {
      const store = useAssetTypeStore.getState()
      store.setLoading(false)
    }
  }

  async updateCustomField(
    assetTypeId: string,
    fieldId: string,
    updates: Partial<CustomField>,
  ): Promise<CustomField | null> {
    try {
      const store = useAssetTypeStore.getState()
      store.setLoading(true)
      store.clearError()

      const updatedField = await this.dbService.updateCustomField(assetTypeId, fieldId, updates)
      
      if (updatedField) {
        // Update the asset type in store
        const updatedAssetType = await this.dbService.getAssetType(assetTypeId)
        if (updatedAssetType) {
          store.updateAssetType(assetTypeId, updatedAssetType)
        }
      }

      return updatedField
    } catch (error) {
      const store = useAssetTypeStore.getState()
      const errorMessage = error instanceof Error ? error.message : "Failed to update custom field"
      store.setError(errorMessage)
      throw error
    } finally {
      const store = useAssetTypeStore.getState()
      store.setLoading(false)
    }
  }

  async removeCustomField(assetTypeId: string, fieldId: string): Promise<boolean> {
    try {
      const store = useAssetTypeStore.getState()
      store.setLoading(true)
      store.clearError()

      const success = await this.dbService.removeCustomField(assetTypeId, fieldId)
      
      if (success) {
        // Update the asset type in store
        const updatedAssetType = await this.dbService.getAssetType(assetTypeId)
        if (updatedAssetType) {
          store.updateAssetType(assetTypeId, updatedAssetType)
        }
      }

      return success
    } catch (error) {
      const store = useAssetTypeStore.getState()
      const errorMessage = error instanceof Error ? error.message : "Failed to remove custom field"
      store.setError(errorMessage)
      throw error
    } finally {
      const store = useAssetTypeStore.getState()
      store.setLoading(false)
    }
  }

  // Lifecycle Management
  async addLifecycleStage(assetTypeId: string, stage: Omit<LifecycleStage, "id">): Promise<LifecycleStage | null> {
    try {
      const store = useAssetTypeStore.getState()
      store.setLoading(true)
      store.clearError()

      const newStage = await this.dbService.addLifecycleStage(assetTypeId, stage)
      
      if (newStage) {
        // Update the asset type in store
        const updatedAssetType = await this.dbService.getAssetType(assetTypeId)
        if (updatedAssetType) {
          store.updateAssetType(assetTypeId, updatedAssetType)
        }
      }

      return newStage
    } catch (error) {
      const store = useAssetTypeStore.getState()
      const errorMessage = error instanceof Error ? error.message : "Failed to add lifecycle stage"
      store.setError(errorMessage)
      throw error
    } finally {
      const store = useAssetTypeStore.getState()
      store.setLoading(false)
    }
  }

  async updateLifecycleStage(
    assetTypeId: string,
    stageId: string,
    updates: Partial<LifecycleStage>,
  ): Promise<LifecycleStage | null> {
    try {
      const store = useAssetTypeStore.getState()
      store.setLoading(true)
      store.clearError()

      const updatedStage = await this.dbService.updateLifecycleStage(assetTypeId, stageId, updates)
      
      if (updatedStage) {
        // Update the asset type in store
        const updatedAssetType = await this.dbService.getAssetType(assetTypeId)
        if (updatedAssetType) {
          store.updateAssetType(assetTypeId, updatedAssetType)
        }
      }

      return updatedStage
    } catch (error) {
      const store = useAssetTypeStore.getState()
      const errorMessage = error instanceof Error ? error.message : "Failed to update lifecycle stage"
      store.setError(errorMessage)
      throw error
    } finally {
      const store = useAssetTypeStore.getState()
      store.setLoading(false)
    }
  }

  // Maintenance Scheduling
  async addMaintenanceSchedule(
    assetTypeId: string,
    schedule: Omit<MaintenanceSchedule, "id">,
  ): Promise<MaintenanceSchedule | null> {
    try {
      const store = useAssetTypeStore.getState()
      store.setLoading(true)
      store.clearError()

      const newSchedule = await this.dbService.addMaintenanceSchedule(assetTypeId, schedule)
      
      if (newSchedule) {
        // Update the asset type in store
        const updatedAssetType = await this.dbService.getAssetType(assetTypeId)
        if (updatedAssetType) {
          store.updateAssetType(assetTypeId, updatedAssetType)
        }
      }

      return newSchedule
    } catch (error) {
      const store = useAssetTypeStore.getState()
      const errorMessage = error instanceof Error ? error.message : "Failed to add maintenance schedule"
      store.setError(errorMessage)
      throw error
    } finally {
      const store = useAssetTypeStore.getState()
      store.setLoading(false)
    }
  }

  async updateMaintenanceSchedule(
    assetTypeId: string,
    scheduleId: string,
    updates: Partial<MaintenanceSchedule>,
  ): Promise<MaintenanceSchedule | null> {
    try {
      const store = useAssetTypeStore.getState()
      store.setLoading(true)
      store.clearError()

      const updatedSchedule = await this.dbService.updateMaintenanceSchedule(assetTypeId, scheduleId, updates)
      
      if (updatedSchedule) {
        // Update the asset type in store
        const updatedAssetType = await this.dbService.getAssetType(assetTypeId)
        if (updatedAssetType) {
          store.updateAssetType(assetTypeId, updatedAssetType)
        }
      }

      return updatedSchedule
    } catch (error) {
      const store = useAssetTypeStore.getState()
      const errorMessage = error instanceof Error ? error.message : "Failed to update maintenance schedule"
      store.setError(errorMessage)
      throw error
    } finally {
      const store = useAssetTypeStore.getState()
      store.setLoading(false)
    }
  }

  // Depreciation Management
  async updateDepreciationSettings(
    assetTypeId: string,
    settings: DepreciationSettings,
  ): Promise<DepreciationSettings | null> {
    try {
      const store = useAssetTypeStore.getState()
      store.setLoading(true)
      store.clearError()

      const updatedSettings = await this.dbService.updateDepreciationSettings(assetTypeId, settings)
      
      if (updatedSettings) {
        // Update the asset type in store
        const updatedAssetType = await this.dbService.getAssetType(assetTypeId)
        if (updatedAssetType) {
          store.updateAssetType(assetTypeId, updatedAssetType)
        }
      }

      return updatedSettings
    } catch (error) {
      const store = useAssetTypeStore.getState()
      const errorMessage = error instanceof Error ? error.message : "Failed to update depreciation settings"
      store.setError(errorMessage)
      throw error
    } finally {
      const store = useAssetTypeStore.getState()
      store.setLoading(false)
    }
  }

  calculateDepreciation(
    assetValue: number,
    settings: DepreciationSettings,
    currentDate: string = new Date().toISOString(),
  ): {
    annualDepreciation: number
    accumulatedDepreciation: number
    bookValue: number
    remainingLife: number
  } {
    const startDate = new Date(settings.startDate)
    const current = new Date(currentDate)
    const yearsElapsed = (current.getTime() - startDate.getTime()) / (365.25 * 24 * 60 * 60 * 1000)

    const salvageValue =
      settings.salvageValueType === "percentage" ? assetValue * (settings.salvageValue / 100) : settings.salvageValue

    const depreciableAmount = assetValue - salvageValue

    let annualDepreciation = 0
    let accumulatedDepreciation = 0

    switch (settings.method) {
      case "straight_line":
        annualDepreciation = depreciableAmount / settings.usefulLife
        accumulatedDepreciation = Math.min(annualDepreciation * yearsElapsed, depreciableAmount)
        break

      case "declining_balance":
        const rate = 1 / settings.usefulLife
        annualDepreciation = (assetValue - accumulatedDepreciation) * rate
        accumulatedDepreciation = assetValue * (1 - Math.pow(1 - rate, yearsElapsed)) - salvageValue
        break

      case "double_declining_balance":
        const doubleRate = 2 / settings.usefulLife
        annualDepreciation = (assetValue - accumulatedDepreciation) * doubleRate
        accumulatedDepreciation = Math.min(assetValue * (1 - Math.pow(1 - doubleRate, yearsElapsed)), depreciableAmount)
        break

      case "sum_of_years_digits":
        const sumOfYears = (settings.usefulLife * (settings.usefulLife + 1)) / 2
        const currentYear = Math.floor(yearsElapsed) + 1
        const remainingYears = Math.max(settings.usefulLife - currentYear + 1, 0)
        annualDepreciation = (depreciableAmount * remainingYears) / sumOfYears

        let totalAccumulated = 0
        for (let year = 1; year <= Math.min(currentYear, settings.usefulLife); year++) {
          const yearFraction = (settings.usefulLife - year + 1) / sumOfYears
          totalAccumulated += depreciableAmount * yearFraction
        }
        accumulatedDepreciation = totalAccumulated
        break

      default:
        annualDepreciation = depreciableAmount / settings.usefulLife
        accumulatedDepreciation = Math.min(annualDepreciation * yearsElapsed, depreciableAmount)
    }

    const bookValue = assetValue - accumulatedDepreciation
    const remainingLife = Math.max(settings.usefulLife - yearsElapsed, 0)

    return {
      annualDepreciation: Math.max(annualDepreciation, 0),
      accumulatedDepreciation: Math.max(accumulatedDepreciation, 0),
      bookValue: Math.max(bookValue, salvageValue),
      remainingLife: Math.max(remainingLife, 0),
    }
  }

  // Category Management
  async createCategory(category: Omit<AssetCategory, "id">): Promise<AssetCategory> {
    try {
      const store = useAssetTypeStore.getState()
      store.setLoading(true)
      store.clearError()

      const newCategory = await this.dbService.createCategory(category)
      
      // Update store
      store.addCategory(newCategory)

      return newCategory
    } catch (error) {
      const store = useAssetTypeStore.getState()
      const errorMessage = error instanceof Error ? error.message : "Failed to create category"
      store.setError(errorMessage)
      throw error
    } finally {
      const store = useAssetTypeStore.getState()
      store.setLoading(false)
    }
  }

  async getCategories(parentId?: string): Promise<AssetCategory[]> {
    try {
      const store = useAssetTypeStore.getState()
      
      // First check store
      const cachedCategories = store.getCategoriesByParent(parentId)
      if (cachedCategories.length > 0) {
        return cachedCategories
      }

      // If not in store, fetch from database
      store.setLoading(true)
      const categories = await this.dbService.getCategories(parentId)
      
      // Update store
      store.setCategories(categories)

      return categories
    } catch (error) {
      const store = useAssetTypeStore.getState()
      const errorMessage = error instanceof Error ? error.message : "Failed to get categories"
      store.setError(errorMessage)
      throw error
    } finally {
      const store = useAssetTypeStore.getState()
      store.setLoading(false)
    }
  }

  // Template Management
  async createTemplate(
    template: Omit<AssetTypeTemplate, "id" | "createdAt" | "usageCount" | "rating">,
  ): Promise<AssetTypeTemplate> {
    try {
      const store = useAssetTypeStore.getState()
      store.setLoading(true)
      store.clearError()

      const newTemplate = await this.dbService.createTemplate(template)
      
      // Update store
      store.addTemplate(newTemplate)

      return newTemplate
    } catch (error) {
      const store = useAssetTypeStore.getState()
      const errorMessage = error instanceof Error ? error.message : "Failed to create template"
      store.setError(errorMessage)
      throw error
    } finally {
      const store = useAssetTypeStore.getState()
      store.setLoading(false)
    }
  }

  async getTemplates(category?: string): Promise<AssetTypeTemplate[]> {
    try {
      const store = useAssetTypeStore.getState()
      store.setLoading(true)
      store.clearError()

      const templates = await this.dbService.getTemplates(category)
      
      // Update store
      store.setTemplates(templates)

      return templates
    } catch (error) {
      const store = useAssetTypeStore.getState()
      const errorMessage = error instanceof Error ? error.message : "Failed to get templates"
      store.setError(errorMessage)
      throw error
    } finally {
      const store = useAssetTypeStore.getState()
      store.setLoading(false)
    }
  }

  async applyTemplate(templateId: string, customizations?: Partial<AssetType>): Promise<Partial<AssetType> | null> {
    try {
      const store = useAssetTypeStore.getState()
      const template = store.templates.find((t) => t.id === templateId)
      
      if (!template) {
        throw new Error("Template not found")
      }

      // Increment usage count in database
      await this.dbService.updateTemplate(templateId, { usageCount: template.usageCount + 1 })
      
      // Update store
      store.updateTemplate(templateId, { usageCount: template.usageCount + 1 })

      return {
        ...template.assetType,
        ...customizations,
      }
    } catch (error) {
      const store = useAssetTypeStore.getState()
      const errorMessage = error instanceof Error ? error.message : "Failed to apply template"
      store.setError(errorMessage)
      throw error
    }
  }

  // Analytics and Metrics
  async getMetrics(): Promise<AssetTypeMetrics> {
    try {
      const store = useAssetTypeStore.getState()
      store.setLoading(true)
      store.clearError()

      const metrics = await this.dbService.getMetrics()
      
      // Update store
      store.setMetrics(metrics)

      return metrics
    } catch (error) {
      const store = useAssetTypeStore.getState()
      const errorMessage = error instanceof Error ? error.message : "Failed to get metrics"
      store.setError(errorMessage)
      throw error
    } finally {
      const store = useAssetTypeStore.getState()
      store.setLoading(false)
    }
  }

  // Validation
  async validateAssetType(assetType: AssetType): Promise<AssetTypeValidationResult> {
    try {
      return await this.dbService.validateAssetType(assetType)
    } catch (error) {
      const store = useAssetTypeStore.getState()
      const errorMessage = error instanceof Error ? error.message : "Failed to validate asset type"
      store.setError(errorMessage)
      throw error
    }
  }

  // Helper methods
  async isAssetTypeInUse(assetTypeId: string): Promise<boolean> {
    try {
      return await this.dbService.isAssetTypeInUse(assetTypeId)
    } catch (error) {
      const store = useAssetTypeStore.getState()
      const errorMessage = error instanceof Error ? error.message : "Failed to check asset type usage"
      store.setError(errorMessage)
      throw error
    }
  }

  async isCustomFieldInUse(assetTypeId: string, fieldId: string): Promise<boolean> {
    try {
      return await this.dbService.isCustomFieldInUse(assetTypeId, fieldId)
    } catch (error) {
      const store = useAssetTypeStore.getState()
      const errorMessage = error instanceof Error ? error.message : "Failed to check custom field usage"
      store.setError(errorMessage)
      throw error
    }
  }

  // Refresh data from database
  async refreshData(): Promise<void> {
    await this.initialize()
  }

  // Clear cache and reload from database
  async clearCache(): Promise<void> {
    const store = useAssetTypeStore.getState()
    store.reset()
    await this.initialize()
  }
}
